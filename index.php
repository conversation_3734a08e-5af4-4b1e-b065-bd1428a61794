<?php
/**
 * الصفحة الرئيسية لنظام إدارة المراكز الصحية
 * Main Page for Healthcare Centers Management System
 */

// التحقق من حالة التثبيت
$isInstalled = file_exists('config/installed.lock');
$hasDatabase = file_exists('config/database.php');

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- SEO Meta Tags -->
    <title>تثبيت نظام إدارة المراكز الصحية | إعداد النظام الطبي</title>
    <meta name="description" content="صفحة تثبيت وإعداد نظام إدارة المراكز الصحية. قم بتثبيت النظام لبدء إدارة التطعيمات والأدوية وسجلات الأطفال في مركزك الصحي.">
    <meta name="keywords" content="تثبيت نظام إدارة المراكز الصحية, إعداد النظام الطبي, تثبيت نظام التطعيمات, إعداد المركز الصحي">
    <meta name="robots" content="noindex, nofollow">

    <!-- Preload Critical Resources -->
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"></noscript>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            direction: rtl;
        }

        .welcome-container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 600px;
            text-align: center;
        }

        .logo {
            font-size: 64px;
            color: #667eea;
            margin-bottom: 20px;
        }

        h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 15px;
        }

        .subtitle {
            color: #666;
            font-size: 16px;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .status-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: right;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .status-item:last-child {
            border-bottom: none;
        }

        .status-icon {
            font-size: 20px;
        }

        .status-icon.success {
            color: #28a745;
        }

        .status-icon.error {
            color: #dc3545;
        }

        .status-icon.warning {
            color: #ffc107;
        }

        .actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 30px;
        }

        .btn {
            padding: 15px 20px;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: white;
        }

        .btn-info {
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .version-info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            font-size: 14px;
            color: #666;
        }

        .features {
            text-align: right;
            margin: 20px 0;
        }

        .features h3 {
            color: #333;
            margin-bottom: 15px;
        }

        .features ul {
            list-style: none;
            padding: 0;
        }

        .features li {
            padding: 8px 0;
            color: #666;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .features li i {
            color: #28a745;
            width: 20px;
        }

        @media (max-width: 768px) {
            .welcome-container {
                margin: 20px;
                padding: 30px 20px;
            }

            .actions {
                grid-template-columns: 1fr;
            }

            h1 {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="welcome-container">
        <div class="logo">
            <i class="fas fa-hospital"></i>
        </div>
        
        <h1>نظام إدارة المراكز الصحية</h1>
        <p class="subtitle">
            نظام شامل لإدارة المراكز الصحية وتتبع التلقيحات والأدوية ووسائل منع الحمل
        </p>

        <!-- حالة النظام -->
        <div class="status-section">
            <h3 style="margin-bottom: 15px; color: #333;">حالة النظام</h3>
            
            <div class="status-item">
                <span>حالة التثبيت</span>
                <span class="status-icon <?php echo $isInstalled ? 'success' : 'error'; ?>">
                    <i class="fas <?php echo $isInstalled ? 'fa-check-circle' : 'fa-times-circle'; ?>"></i>
                    <?php echo $isInstalled ? 'مثبت' : 'غير مثبت'; ?>
                </span>
            </div>
            
            <div class="status-item">
                <span>قاعدة البيانات</span>
                <span class="status-icon <?php echo $hasDatabase ? 'success' : 'error'; ?>">
                    <i class="fas <?php echo $hasDatabase ? 'fa-check-circle' : 'fa-times-circle'; ?>"></i>
                    <?php echo $hasDatabase ? 'متصلة' : 'غير متصلة'; ?>
                </span>
            </div>
            
            <div class="status-item">
                <span>إصدار PHP</span>
                <span class="status-icon <?php echo version_compare(PHP_VERSION, '7.4.0', '>=') ? 'success' : 'warning'; ?>">
                    <i class="fas fa-info-circle"></i>
                    <?php echo PHP_VERSION; ?>
                </span>
            </div>
        </div>

        <!-- المميزات -->
        <div class="features">
            <h3>المميزات الرئيسية</h3>
            <ul>
                <li><i class="fas fa-baby"></i> إدارة شاملة للأطفال والتلقيحات</li>
                <li><i class="fas fa-syringe"></i> تتبع اللقاحات والمخزون</li>
                <li><i class="fas fa-pills"></i> إدارة الأدوية ووسائل منع الحمل</li>
                <li><i class="fas fa-chart-bar"></i> تقارير وإحصائيات متقدمة</li>
                <li><i class="fas fa-users"></i> نظام صلاحيات متعدد المستويات</li>
                <li><i class="fas fa-mobile-alt"></i> متوافق مع جميع الأجهزة</li>
            </ul>
        </div>

        <!-- الإجراءات -->
        <div class="actions">
            <?php if (!$isInstalled): ?>
                <a href="install.php" class="btn btn-primary">
                    <i class="fas fa-download"></i>
                    تثبيت النظام
                </a>
            <?php else: ?>
                <a href="cs-manager-api.html" class="btn btn-success">
                    <i class="fas fa-rocket"></i>
                    النسخة الجديدة (API)
                </a>
                
                <a href="cs-manager.html" class="btn btn-info">
                    <i class="fas fa-desktop"></i>
                    النسخة القديمة
                </a>
            <?php endif; ?>
            
            <a href="system-test.php" class="btn btn-warning">
                <i class="fas fa-vial"></i>
                اختبار النظام
            </a>
            
            <a href="README.md" class="btn btn-secondary">
                <i class="fas fa-book"></i>
                دليل المستخدم
            </a>
        </div>

        <!-- معلومات الإصدار -->
        <div class="version-info">
            <strong>الإصدار:</strong> 2.0.0 (مع قاعدة البيانات)<br>
            <strong>تاريخ التحديث:</strong> <?php echo date('Y-m-d'); ?><br>
            <strong>المطور:</strong> فريق تطوير الأنظمة الصحية
        </div>

        <?php if ($isInstalled): ?>
        <div style="margin-top: 20px; padding: 15px; background: #d4edda; border-radius: 8px; color: #155724;">
            <i class="fas fa-info-circle"></i>
            <strong>ملاحظة:</strong> النظام جاهز للاستخدام. يمكنك الآن الوصول إلى النسخة الجديدة التي تستخدم قاعدة البيانات MySQL.
        </div>
        <?php else: ?>
        <div style="margin-top: 20px; padding: 15px; background: #fff3cd; border-radius: 8px; color: #856404;">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>تنبيه:</strong> يجب تثبيت النظام أولاً قبل الاستخدام. انقر على "تثبيت النظام" للبدء.
        </div>
        <?php endif; ?>
    </div>

    <script>
        // إضافة تأثيرات تفاعلية
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير hover للأزرار
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(button => {
                button.addEventListener('mouseenter', function() {
                    this.style.boxShadow = '0 8px 25px rgba(0,0,0,0.15)';
                });
                
                button.addEventListener('mouseleave', function() {
                    this.style.boxShadow = 'none';
                });
            });

            // فحص حالة النظام كل 30 ثانية
            setInterval(function() {
                // يمكن إضافة فحص AJAX هنا لتحديث حالة النظام
            }, 30000);
        });
    </script>
</body>
</html>
