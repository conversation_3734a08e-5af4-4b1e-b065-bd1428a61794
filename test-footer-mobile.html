<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الفوتر للهواتف المحمولة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            direction: rtl;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .main-content {
            flex: 1;
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            margin-top: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .test-controls {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }

        .test-button {
            display: inline-block;
            padding: 10px 15px;
            margin: 5px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.2s;
        }

        .test-button:hover {
            background: #0056b3;
        }

        .test-button.dark {
            background: #343a40;
        }

        .device-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 14px;
        }

        /* نسخ أنماط الفوتر من الملف الرئيسي */
        .developer-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            color: #ffffff;
            padding: 40px 20px 20px;
            margin-top: 30px;
            border-top: 3px solid #ffd89b;
            position: relative;
            z-index: 1;
            width: 100%;
            box-sizing: border-box;
            transition: all 0.3s ease;
            box-shadow: 0 -4px 20px rgba(102, 126, 234, 0.3);
        }

        .footer-content {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: 40px;
            max-width: 1200px;
            margin: 0 auto;
            position: relative;
        }

        .footer-content::before {
            content: '';
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 4px;
            background: linear-gradient(90deg, #ffd89b, #19547b, #ffd89b);
            border-radius: 2px;
            box-shadow: 0 2px 8px rgba(255, 216, 155, 0.5);
        }

        .tool-description {
            flex: 1;
            text-align: right;
        }

        .tool-description h3 {
            font-size: 1.5rem;
            margin-bottom: 10px;
            color: #ffffff;
            transition: all 0.3s ease;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            font-weight: 700;
        }

        .tool-description p {
            font-size: 1.1rem;
            margin-bottom: 15px;
            color: #f1f5f9;
            line-height: 1.6;
            transition: all 0.3s ease;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .features {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: flex-end;
        }

        .features span {
            background: rgba(255, 255, 255, 0.2);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            color: #ffffff;
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
            font-weight: 500;
            backdrop-filter: blur(10px);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .features span:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .developer-section {
            flex: 1;
            text-align: left;
        }

        .developer-container {
            display: flex !important;
            align-items: center !important;
            justify-content: space-between !important;
            gap: 10px !important;
            padding: 8px 88px 5px 55px !important;
            background: rgba(255, 255, 255, 0.15) !important;
            border-radius: 13px !important;
            backdrop-filter: blur(10px) !important;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease !important;
            flex-direction: column !important;
            align-content: stretch !important;
        }

        .developer-container:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);
            background: rgba(255, 255, 255, 0.2);
        }

        .developer-avatar {
            font-size: 3rem;
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            border-radius: 50%;
            width: 80px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
            transition: all 0.3s ease;
        }

        .developer-avatar:hover {
            transform: scale(1.05);
            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
        }

        .developer-details {
            text-align: left;
        }

        .developer-name {
            font-size: 1.4rem;
            font-weight: bold;
            color: #ffffff;
            text-transform: uppercase;
            margin-bottom: 5px;
            transition: all 0.3s ease;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .developer-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #f8fafc;
            margin-bottom: 3px;
            transition: all 0.3s ease;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .developer-subtitle {
            font-size: 0.9rem;
            color: #e2e8f0;
            font-style: italic;
            transition: all 0.3s ease;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .developer-title2 {
            font-size: 1rem;
            font-weight: 600;
            color: #f8fafc;
            margin-bottom: 3px;
            transition: all 0.3s ease;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .developer-subtitle2 {
            font-style: italic;
            font-size: 14px;
            color: #ececec;
        }

        .haha {
            font-size: 0.9rem;
            color: #e2e8f0;
            margin-bottom: 8px;
            transition: all 0.3s ease;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .developer-credit {
            font-size: 0.9rem;
            color: #e2e8f0;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            padding-top: 15px;
            margin-top: 30px;
            text-align: center;
            transition: all 0.3s ease;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        /* تحسينات الفوتر للهواتف المحمولة - جزأين بجانب بعض مع ألوان محسنة */
        @media (max-width: 768px) {
            .developer-info {
                padding: 15px 10px;
                margin-top: 20px;
                background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                border: none;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
                border-radius: 20px;
            }

            .footer-content {
                flex-direction: row;
                gap: 12px;
                max-width: 100%;
                margin: 0;
                align-items: stretch;
            }

            .footer-content::before {
                display: none;
            }

            /* الجزء الأول: معلومات المطور */
            .developer-section {
                order: 1;
                flex: 1;
                background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
                padding: 18px 12px;
                border-radius: 15px;
                box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3);
                text-align: center;
                display: flex;
                flex-direction: column;
                justify-content: center;
            }

            .developer-container {
                padding: 0 !important;
                background: none !important;
                border: none !important;
                border-radius: 0 !important;
                backdrop-filter: none !important;
                gap: 8px !important;
                height: 100%;
                display: flex !important;
                flex-direction: column !important;
                justify-content: center !important;
            }

            .developer-avatar {
                width: 55px;
                height: 55px;
                font-size: 2rem;
                margin: 0 auto 10px auto;
                background: linear-gradient(135deg, #fbbf24, #f59e0b);
                box-shadow: 0 3px 12px rgba(251, 191, 36, 0.4);
            }

            .developer-details {
                text-align: center;
            }

            .haha {
                font-size: 0.75rem;
                margin-bottom: 6px;
                color: #e0e7ff;
                font-weight: 500;
            }

            .developer-name {
                font-size: 1.1rem;
                margin-bottom: 4px;
                color: #ffffff;
                font-weight: 700;
            }

            .developer-title {
                font-size: 0.8rem;
                margin-bottom: 3px;
                color: #c7d2fe;
                font-weight: 500;
            }

            .developer-subtitle {
                font-size: 0.7rem;
                margin-bottom: 8px;
                color: #a5b4fc;
                font-style: italic;
            }

            .developer-title2 {
                font-size: 0.8rem;
                margin-bottom: 3px;
                color: #c7d2fe;
                font-weight: 500;
            }

            .developer-subtitle2 {
                font-size: 0.7rem;
                color: #a5b4fc;
                font-style: italic;
            }

            /* الجزء الثاني: وصف الموقع */
            .tool-description {
                order: 2;
                flex: 1;
                background: linear-gradient(135deg, #059669 0%, #047857 100%);
                padding: 18px 12px;
                border-radius: 15px;
                text-align: center;
                box-shadow: 0 4px 15px rgba(5, 150, 105, 0.3);
                display: flex;
                flex-direction: column;
                justify-content: center;
            }

            .tool-description h3 {
                font-size: 1.1rem;
                margin-bottom: 8px;
                color: #ffffff;
                text-shadow: none;
                font-weight: 700;
                line-height: 1.3;
            }

            .tool-description p {
                font-size: 0.8rem;
                margin-bottom: 0;
                color: #d1fae5;
                line-height: 1.4;
                text-shadow: none;
                font-weight: 400;
            }

            /* إخفاء الميزات للهواتف */
            .features {
                display: none;
            }

            .developer-credit {
                order: 3;
                font-size: 0.75rem;
                padding: 12px;
                margin-top: 12px;
                text-align: center;
                background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
                border-radius: 12px;
                color: #ffffff;
                border: none;
                line-height: 1.4;
                font-weight: 500;
                box-shadow: 0 3px 10px rgba(99, 102, 241, 0.3);
            }

            /* إزالة التأثيرات للهواتف */
            .developer-container:hover {
                transform: none;
                box-shadow: none;
                background: none !important;
            }

            .developer-avatar:hover {
                transform: none;
            }
        }

        @media (max-width: 480px) {
            .developer-info {
                padding: 15px 10px 12px;
                margin-top: 15px;
            }

            .footer-content {
                gap: 15px;
            }

            .footer-content::before {
                width: 30px;
                height: 2px;
                top: -12px;
            }

            .tool-description h3 {
                font-size: 1.1rem;
                margin-bottom: 6px;
            }

            .tool-description p {
                font-size: 0.9rem;
                margin-bottom: 10px;
                line-height: 1.4;
            }

            .features {
                gap: 6px;
            }

            .features span {
                font-size: 0.75rem;
                padding: 5px 10px;
                border-radius: 12px;
            }

            .developer-container {
                padding: 12px 15px 10px 15px !important;
                gap: 6px !important;
                border-radius: 10px !important;
            }

            .developer-avatar {
                width: 50px;
                height: 50px;
                font-size: 2rem;
            }

            .haha {
                font-size: 0.75rem;
                margin-bottom: 5px;
            }

            .developer-name {
                font-size: 1rem;
                margin-bottom: 3px;
            }

            .developer-title {
                font-size: 0.8rem;
                margin-bottom: 2px;
            }

            .developer-subtitle {
                font-size: 0.75rem;
                margin-bottom: 6px;
            }

            .developer-title2 {
                font-size: 0.8rem;
                margin-bottom: 2px;
            }

            .developer-subtitle2 {
                font-size: 0.7rem;
            }

            .developer-credit {
                font-size: 0.75rem;
                padding-top: 10px;
                margin-top: 15px;
                line-height: 1.3;
            }
        }

        /* الوضع المظلم */
        [data-theme="dark"] .developer-info {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            color: #f1f5f9;
            border-top-color: #3b82f6;
            box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3);
        }

        [data-theme="dark"] .tool-description h3 {
            color: #60a5fa;
        }

        [data-theme="dark"] .tool-description p {
            color: #cbd5e1;
        }

        [data-theme="dark"] .features span {
            background: rgba(59, 130, 246, 0.15);
            color: #93c5fd;
            border-color: rgba(59, 130, 246, 0.3);
        }

        [data-theme="dark"] .developer-name {
            color: #ffffff;
        }

        [data-theme="dark"] .developer-title {
            color: #cbd5e1;
        }

        [data-theme="dark"] .developer-subtitle {
            color: #94a3b8;
        }

        [data-theme="dark"] .developer-title2 {
            color: #cbd5e1;
        }

        [data-theme="dark"] .developer-subtitle2 {
            color: #94a3b8;
        }

        [data-theme="dark"] .developer-credit {
            color: #64748b;
            border-top-color: #475569;
        }

        @media (max-width: 768px) {
            [data-theme="dark"] .developer-info {
                background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            }

            /* الجزء الأول: معلومات المطور - وضع مظلم */
            [data-theme="dark"] .developer-section {
                background: linear-gradient(135deg, #312e81 0%, #1e1b4b 100%);
                box-shadow: 0 4px 15px rgba(49, 46, 129, 0.4);
            }

            [data-theme="dark"] .developer-avatar {
                background: linear-gradient(135deg, #f59e0b, #d97706);
                box-shadow: 0 3px 12px rgba(245, 158, 11, 0.5);
            }

            [data-theme="dark"] .haha {
                color: #c7d2fe;
            }

            [data-theme="dark"] .developer-name {
                color: #ffffff;
            }

            [data-theme="dark"] .developer-title {
                color: #e0e7ff;
            }

            [data-theme="dark"] .developer-subtitle {
                color: #c7d2fe;
            }

            [data-theme="dark"] .developer-title2 {
                color: #e0e7ff;
            }

            [data-theme="dark"] .developer-subtitle2 {
                color: #c7d2fe;
            }

            /* الجزء الثاني: وصف الموقع - وضع مظلم */
            [data-theme="dark"] .tool-description {
                background: linear-gradient(135deg, #064e3b 0%, #065f46 100%);
                box-shadow: 0 4px 15px rgba(6, 78, 59, 0.4);
            }

            [data-theme="dark"] .tool-description h3 {
                color: #ffffff;
            }

            [data-theme="dark"] .tool-description p {
                color: #d1fae5;
            }

            [data-theme="dark"] .developer-credit {
                background: linear-gradient(135deg, #4338ca 0%, #6d28d9 100%);
                color: #ffffff;
                box-shadow: 0 3px 10px rgba(67, 56, 202, 0.4);
            }

            [data-theme="dark"] .developer-container:hover {
                background: none !important;
            }

            [data-theme="dark"] .developer-avatar:hover {
                box-shadow: 0 3px 12px rgba(245, 158, 11, 0.6);
            }
        }
    </style>
</head>
<body>
    <div class="main-content">
        <h1>اختبار الفوتر للهواتف المحمولة</h1>
        
        <div class="device-info">
            <strong>معلومات الجهاز:</strong><br>
            عرض الشاشة: <span id="screenWidth"></span>px<br>
            ارتفاع الشاشة: <span id="screenHeight"></span>px<br>
            نوع الجهاز: <span id="deviceType"></span><br>
            الاتجاه: <span id="orientation"></span>
        </div>

        <div class="test-controls">
            <strong>أدوات الاختبار:</strong><br>
            <button class="test-button" onclick="toggleTheme()">تبديل الوضع المظلم/الفاتح</button>
            <button class="test-button" onclick="simulateRotation()">محاكاة تدوير الشاشة</button>
            <button class="test-button" onclick="testResponsiveness()">اختبار التجاوب</button>
        </div>

        <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin: 20px 0;">
            <strong>تعليمات الاختبار:</strong><br>
            • اختبر الفوتر على أحجام شاشات مختلفة<br>
            • جرب تدوير الشاشة لرؤية التكيف<br>
            • اختبر الوضع المظلم والفاتح<br>
            • تأكد من وضوح النصوص وسهولة القراءة<br>
            • لاحظ كيف يتم ترتيب العناصر في الهواتف المحمولة
        </div>

        <p>هذا محتوى تجريبي لاختبار كيفية ظهور الفوتر في أسفل الصفحة. يمكنك تمرير الصفحة للأسفل لرؤية الفوتر المحسن للهواتف المحمولة.</p>
        
        <div style="height: 200px; background: linear-gradient(45deg, #f0f0f0, #e0e0e0); border-radius: 8px; display: flex; align-items: center; justify-content: center; margin: 20px 0;">
            <p style="color: #666; font-size: 18px;">محتوى إضافي للصفحة</p>
        </div>
    </div>

    <!-- الفوتر المحسن -->
    <footer class="developer-info">
        <div class="footer-content">
            <div class="tool-description">
                <h3>🏥 نظام إدارة المراكز الصحية الشامل</h3>
                <p>نظام رقمي متطور لإدارة جميع أنشطة المراكز الصحية والعيادات الطبية من تطعيمات وأدوية وتنظيم أسرة وسجلات المرضى</p>
                <div class="features">
                    <span>📅 حساب مواعيد التطعيمات الإجبارية</span>
                    <span>💉 إدارة مخزون اللقاحات والتطعيمات</span>
                    <span>💊 إدارة مخزون الأدوية والمستلزمات</span>
                    <span>👨‍👩‍👧‍👦 خدمات تنظيم الأسرة ووسائل منع الحمل</span>
                    <span>👶 سجل شامل للأطفال والمرضى</span>
                    <span>📊 تقارير PDF تفصيلية ومتقدمة</span>
                </div>
            </div>

            <div class="developer-section">
                <div class="developer-container">
                    <div class="developer-avatar">👨‍⚕️</div>
                    <div class="developer-details">
                        <div class="haha">تمت برمجة هذه الأداة من طرف : </div>
                        <div class="developer-name professional-text">Jamal Chafik</div>
                        <div class="developer-title">Infirmier Diplômé d'État</div>
                        <div class="developer-subtitle">Infirmier Polyvalent</div>
                        <div class="developer-title2">بتعاون مع : Youness Amarir</div>
                        <div class="developer-subtitle2">infirmier en santé communautaire</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="developer-credit">
            version 1.0 - النسخة الأولية من التطبيق ( قيد التجربة - أترك لننا رأيك في الشريط الجانبي الأيسر )
        </div>
    </footer>

    <script>
        // تحديث معلومات الجهاز
        function updateDeviceInfo() {
            document.getElementById('screenWidth').textContent = window.innerWidth;
            document.getElementById('screenHeight').textContent = window.innerHeight;
            document.getElementById('deviceType').textContent = window.innerWidth <= 768 ? 'هاتف محمول' : 'شاشة كبيرة';
            document.getElementById('orientation').textContent = window.innerWidth > window.innerHeight ? 'أفقي' : 'عمودي';
        }

        // تبديل الوضع المظلم
        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            document.documentElement.setAttribute('data-theme', newTheme);
        }

        // محاكاة تدوير الشاشة
        function simulateRotation() {
            alert('قم بتدوير جهازك لرؤية كيفية تكيف الفوتر مع الاتجاه الجديد');
        }

        // اختبار التجاوب
        function testResponsiveness() {
            const sizes = [
                { width: 320, name: 'هاتف صغير' },
                { width: 375, name: 'هاتف متوسط' },
                { width: 414, name: 'هاتف كبير' },
                { width: 768, name: 'تابلت' },
                { width: 1024, name: 'شاشة كبيرة' }
            ];
            
            let currentIndex = 0;
            const interval = setInterval(() => {
                if (currentIndex >= sizes.length) {
                    clearInterval(interval);
                    return;
                }
                
                const size = sizes[currentIndex];
                alert(`اختبار حجم: ${size.name} (${size.width}px)`);
                currentIndex++;
            }, 2000);
        }

        // استدعاء عند تحميل الصفحة وتغيير حجم الشاشة
        updateDeviceInfo();
        window.addEventListener('resize', updateDeviceInfo);
        window.addEventListener('orientationchange', () => {
            setTimeout(updateDeviceInfo, 100);
        });
    </script>
</body>
</html>
