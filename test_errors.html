<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح الأخطاء</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 3px;
            font-family: monospace;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        .error-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 اختبار إصلاح الأخطاء</h1>
        
        <div class="test-section">
            <h3>🎯 اختبار e.target.closest</h3>
            <button class="test-button" onclick="testClosestFunction()">اختبار دالة closest</button>
            <div id="closestResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🔔 اختبار updateNotificationBadge</h3>
            <button class="test-button" onclick="testNotificationBadge()">اختبار دالة الإشعارات</button>
            <div id="notificationResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📚 اختبار تحميل المكتبات</h3>
            <button class="test-button" onclick="testLibraries()">فحص المكتبات</button>
            <button class="test-button" onclick="attemptReload()" style="background: #28a745;">إعادة تحميل المكتبات</button>
            <button class="test-button" onclick="forceReloadPage()" style="background: #dc3545;">إعادة تحميل الصفحة</button>
            <div id="librariesResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🖱️ اختبار مستمعي الأحداث</h3>
            <button class="test-button test-hover-element" onclick="testEventListeners()">اختبار الأحداث</button>
            <div id="eventsResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📋 سجل الأخطاء</h3>
            <button class="test-button" onclick="showErrorLog()">عرض سجل الأخطاء</button>
            <div id="errorLog" class="error-log"></div>
        </div>
    </div>

    <script>
        // تجميع الأخطاء
        const errorLog = [];
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;

        console.error = function(...args) {
            errorLog.push({type: 'error', message: args.join(' '), time: new Date().toLocaleTimeString()});
            originalConsoleError.apply(console, args);
        };

        console.warn = function(...args) {
            errorLog.push({type: 'warning', message: args.join(' '), time: new Date().toLocaleTimeString()});
            originalConsoleWarn.apply(console, args);
        };

        // محاكاة updateNotificationBadge
        function updateNotificationBadge() {
            console.log('✅ دالة updateNotificationBadge تعمل بشكل صحيح');
            return true;
        }

        function testClosestFunction() {
            try {
                // إنشاء عنصر تجريبي
                const testDiv = document.createElement('div');
                testDiv.className = 'test-element';
                testDiv.innerHTML = '<span class="inner">نص تجريبي</span>';
                document.body.appendChild(testDiv);

                // محاكاة حدث
                const mockEvent = {
                    target: testDiv.querySelector('.inner')
                };

                // اختبار الدالة
                if (!mockEvent.target || typeof mockEvent.target.closest !== 'function') {
                    throw new Error('e.target.closest غير متوفرة');
                }

                const result = mockEvent.target.closest('.test-element');
                if (result) {
                    showResult('closestResult', '✅ دالة e.target.closest تعمل بشكل صحيح', 'success');
                } else {
                    throw new Error('فشل في العثور على العنصر الأب');
                }

                // تنظيف
                document.body.removeChild(testDiv);

            } catch (error) {
                showResult('closestResult', '❌ خطأ في دالة closest: ' + error.message, 'error');
            }
        }

        function testNotificationBadge() {
            try {
                if (typeof updateNotificationBadge === 'function') {
                    updateNotificationBadge();
                    showResult('notificationResult', '✅ دالة updateNotificationBadge موجودة وتعمل', 'success');
                } else {
                    throw new Error('دالة updateNotificationBadge غير موجودة');
                }
            } catch (error) {
                showResult('notificationResult', '❌ خطأ في دالة الإشعارات: ' + error.message, 'error');
            }
        }

        function testLibraries() {
            const libraries = {
                'jQuery': typeof window.$ !== 'undefined',
                'jsPDF': typeof window.jsPDF !== 'undefined',
                'html2canvas': typeof window.html2canvas !== 'undefined'
            };

            let resultText = '📚 حالة المكتبات:\n';
            let allLoaded = true;
            let loadedCount = 0;

            Object.entries(libraries).forEach(([name, loaded]) => {
                resultText += `${loaded ? '✅' : '❌'} ${name}: ${loaded ? 'محملة' : 'غير محملة'}\n`;
                if (!loaded) {
                    allLoaded = false;
                } else {
                    loadedCount++;
                }
            });

            resultText += `\n📊 المحملة: ${loadedCount}/${Object.keys(libraries).length}`;

            // إضافة معلومات إضافية عن المكتبات المحملة
            if (typeof window.$ !== 'undefined') {
                resultText += `\n🔧 jQuery الإصدار: ${$.fn.jquery || 'غير معروف'}`;
            }

            if (typeof window.jsPDF !== 'undefined') {
                resultText += `\n📄 jsPDF متوفرة`;
            }

            if (typeof window.html2canvas !== 'undefined') {
                resultText += `\n🖼️ html2canvas متوفرة`;
            }

            const resultType = allLoaded ? 'success' : (loadedCount > 0 ? 'warning' : 'error');
            showResult('librariesResult', resultText, resultType);

            // محاولة إعادة تحميل المكتبات المفقودة
            if (!allLoaded) {
                setTimeout(attemptReload, 2000);
            }
        }

        function attemptReload() {
            console.log('🔄 محاولة إعادة تحميل المكتبات...');

            // إعادة تحميل jQuery إذا لم تكن محملة
            if (typeof window.$ === 'undefined') {
                loadScript('https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js', 'jQuery');
            }

            // إعادة تحميل jsPDF إذا لم تكن محملة
            if (typeof window.jsPDF === 'undefined') {
                loadScript('https://unpkg.com/jspdf@2.5.1/dist/jspdf.umd.min.js', 'jsPDF');
            }

            // إعادة تحميل html2canvas إذا لم تكن محملة
            if (typeof window.html2canvas === 'undefined') {
                loadScript('https://unpkg.com/html2canvas@1.4.1/dist/html2canvas.min.js', 'html2canvas');
            }

            // فحص مرة أخرى بعد 3 ثواني
            setTimeout(() => {
                console.log('🔍 فحص نهائي للمكتبات...');
                testLibraries();
            }, 3000);
        }

        function loadScript(src, name) {
            const script = document.createElement('script');
            script.src = src;
            script.onload = () => {
                console.log(`✅ تم تحميل ${name} بنجاح`);
                showResult('librariesResult', `🔄 تم تحميل ${name} بنجاح!`, 'info');
            };
            script.onerror = () => {
                console.error(`❌ فشل في تحميل ${name}`);
                showResult('librariesResult', `❌ فشل في تحميل ${name}`, 'error');
            };
            document.head.appendChild(script);
        }

        function forceReloadPage() {
            if (confirm('هل تريد إعادة تحميل الصفحة؟ سيتم فقدان البيانات غير المحفوظة.')) {
                window.location.reload();
            }
        }

        function testEventListeners() {
            try {
                let testsPassed = 0;
                const totalTests = 3;

                // اختبار 1: التحقق من وجود مستمعي الأحداث
                if (document.addEventListener) {
                    testsPassed++;
                }

                // اختبار 2: محاكاة حدث click
                const mockClickEvent = new Event('click');
                mockClickEvent.target = document.querySelector('.test-hover-element');
                
                if (mockClickEvent.target && typeof mockClickEvent.target.closest === 'function') {
                    testsPassed++;
                }

                // اختبار 3: محاكاة حدث mouseenter
                const mockMouseEvent = new Event('mouseenter');
                mockMouseEvent.target = document.querySelector('.test-hover-element');
                
                if (mockMouseEvent.target && typeof mockMouseEvent.target.closest === 'function') {
                    testsPassed++;
                }

                const resultText = `✅ نجح ${testsPassed} من ${totalTests} اختبارات الأحداث`;
                const resultType = testsPassed === totalTests ? 'success' : 'warning';
                showResult('eventsResult', resultText, resultType);

            } catch (error) {
                showResult('eventsResult', '❌ خطأ في اختبار الأحداث: ' + error.message, 'error');
            }
        }

        function showErrorLog() {
            const logElement = document.getElementById('errorLog');
            
            if (errorLog.length === 0) {
                logElement.innerHTML = '<div class="success">✅ لا توجد أخطاء مسجلة!</div>';
                return;
            }

            let logHtml = '';
            errorLog.forEach((entry, index) => {
                const typeIcon = entry.type === 'error' ? '❌' : '⚠️';
                const typeClass = entry.type === 'error' ? 'error' : 'warning';
                
                logHtml += `
                    <div class="${typeClass}" style="margin: 5px 0; padding: 5px; border-radius: 3px;">
                        <strong>${typeIcon} [${entry.time}]</strong><br>
                        ${entry.message}
                    </div>
                `;
            });

            logElement.innerHTML = logHtml;
        }

        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = 'result ' + type;
        }

        // تشغيل اختبارات تلقائية عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 بدء اختبارات إصلاح الأخطاء...');
            
            setTimeout(() => {
                testClosestFunction();
                testNotificationBadge();
                testLibraries();
                testEventListeners();
            }, 500);
        });

        // اختبار معالجة الأخطاء
        window.addEventListener('error', function(e) {
            errorLog.push({
                type: 'error', 
                message: `JavaScript Error: ${e.message} في ${e.filename}:${e.lineno}`, 
                time: new Date().toLocaleTimeString()
            });
        });
    </script>
</body>
</html>
