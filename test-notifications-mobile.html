<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الإشعارات للهواتف المحمولة</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            direction: rtl;
        }

        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .test-button {
            display: block;
            width: 100%;
            padding: 15px;
            margin: 10px 0;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: background 0.2s;
        }

        .test-button:hover {
            background: #0056b3;
        }

        .test-button.success {
            background: #28a745;
        }

        .test-button.warning {
            background: #ffc107;
            color: #212529;
        }

        .test-button.error {
            background: #dc3545;
        }

        .info-box {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }

        .device-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 14px;
        }

        /* نسخ أنماط الإشعارات من الملف الرئيسي */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1002;
            display: flex;
            flex-direction: column;
            gap: 12px;
            max-width: calc(100vw - 40px);
            width: auto;
        }

        .toast {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            padding: 16px 20px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(10px);
            transform: translateX(400px);
            opacity: 0;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            max-width: 400px;
            min-width: 300px;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        .toast.show {
            transform: translateX(0);
            opacity: 1;
        }

        .toast.success {
            border-left: 4px solid #10b981;
        }

        .toast.error {
            border-left: 4px solid #ef4444;
        }

        .toast.warning {
            border-left: 4px solid #f59e0b;
        }

        .toast.info {
            border-left: 4px solid #3b82f6;
        }

        .toast-content {
            display: flex;
            align-items: flex-start;
            gap: 12px;
        }

        .toast-icon {
            font-size: 1.2rem;
            flex-shrink: 0;
            margin-top: 2px;
        }

        .toast-message {
            flex: 1;
            font-weight: 500;
            color: #333;
            line-height: 1.4;
            word-break: break-word;
            hyphens: auto;
        }

        .toast-close {
            background: none;
            border: none;
            color: #666;
            cursor: pointer;
            font-size: 1.1rem;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.2s ease;
            flex-shrink: 0;
            margin-top: 2px;
        }

        .toast-close:hover {
            background: #f0f0f0;
            color: #333;
        }

        /* تحسينات للهواتف المحمولة */
        @media (max-width: 768px) {
            .toast-container {
                top: 10px;
                right: 10px;
                left: 10px;
                max-width: none;
                width: auto;
            }

            .toast {
                max-width: none;
                min-width: auto;
                width: 100%;
                padding: 14px 16px;
                font-size: 14px;
                border-radius: 12px;
                transform: translateY(-100px);
            }

            .toast.show {
                transform: translateY(0);
            }

            .toast-content {
                gap: 10px;
            }

            .toast-icon {
                font-size: 1.1rem;
            }

            .toast-message {
                font-size: 14px;
                line-height: 1.3;
            }

            .toast-close {
                font-size: 1rem;
                padding: 6px;
                min-width: 32px;
                min-height: 32px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }

        @media (max-width: 480px) {
            .toast-container {
                top: 8px;
                right: 8px;
                left: 8px;
            }

            .toast {
                padding: 12px 14px;
                font-size: 13px;
                border-radius: 10px;
            }

            .toast-content {
                gap: 8px;
            }

            .toast-icon {
                font-size: 1rem;
            }

            .toast-message {
                font-size: 13px;
                line-height: 1.2;
            }

            .toast-close {
                font-size: 0.9rem;
                padding: 4px;
                min-width: 28px;
                min-height: 28px;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>اختبار الإشعارات للهواتف المحمولة</h1>
        
        <div class="device-info">
            <strong>معلومات الجهاز:</strong><br>
            عرض الشاشة: <span id="screenWidth"></span>px<br>
            ارتفاع الشاشة: <span id="screenHeight"></span>px<br>
            نوع الجهاز: <span id="deviceType"></span><br>
            الاتجاه: <span id="orientation"></span>
        </div>

        <div class="info-box">
            <strong>تعليمات الاختبار:</strong><br>
            • اضغط على الأزرار لاختبار أنواع مختلفة من الإشعارات<br>
            • جرب السحب لإغلاق الإشعارات على الهواتف المحمولة<br>
            • اختبر تدوير الشاشة لرؤية التكيف مع الاتجاه<br>
            • تأكد من أن النصوص واضحة ومقروءة
        </div>

        <button class="test-button success" onclick="testToast('تم حفظ البيانات بنجاح!', 'success')">
            إشعار نجاح
        </button>

        <button class="test-button error" onclick="testToast('حدث خطأ في الاتصال بالخادم', 'error')">
            إشعار خطأ
        </button>

        <button class="test-button warning" onclick="testToast('تحذير: يرجى التحقق من البيانات المدخلة', 'warning')">
            إشعار تحذير
        </button>

        <button class="test-button" onclick="testToast('معلومات: تم تحديث النظام إلى الإصدار الجديد', 'info')">
            إشعار معلومات
        </button>

        <button class="test-button" onclick="testLongMessage()">
            رسالة طويلة
        </button>

        <button class="test-button" onclick="testMultipleToasts()">
            إشعارات متعددة
        </button>

        <button class="test-button" onclick="clearAllToasts()">
            مسح جميع الإشعارات
        </button>
    </div>

    <!-- حاوي الإشعارات -->
    <div id="toast-container" class="toast-container"></div>

    <script>
        // تحديث معلومات الجهاز
        function updateDeviceInfo() {
            document.getElementById('screenWidth').textContent = window.innerWidth;
            document.getElementById('screenHeight').textContent = window.innerHeight;
            document.getElementById('deviceType').textContent = window.innerWidth <= 768 ? 'هاتف محمول' : 'شاشة كبيرة';
            document.getElementById('orientation').textContent = window.innerWidth > window.innerHeight ? 'أفقي' : 'عمودي';
        }

        // استدعاء عند تحميل الصفحة وتغيير حجم الشاشة
        updateDeviceInfo();
        window.addEventListener('resize', updateDeviceInfo);
        window.addEventListener('orientationchange', () => {
            setTimeout(updateDeviceInfo, 100);
        });

        // دالة إظهار الإشعار
        function showToast(message, type = 'info', duration = 3000) {
            const container = document.getElementById('toast-container');
            if (!container) return;

            const isMobile = window.innerWidth <= 768;
            const adjustedDuration = isMobile ? Math.max(duration * 1.2, 4000) : duration;

            const toast = document.createElement('div');
            toast.className = `toast ${type}`;

            const icons = {
                success: '✓',
                error: '✕',
                warning: '⚠',
                info: 'ℹ'
            };

            let displayMessage = message;
            if (isMobile && message.length > 120) {
                displayMessage = message.substring(0, 117) + '...';
            }

            toast.innerHTML = `
                <div class="toast-content">
                    <span class="toast-icon">${icons[type] || icons.info}</span>
                    <span class="toast-message" title="${message}">${displayMessage}</span>
                    <button class="toast-close" onclick="hideToast(this)" aria-label="إغلاق الإشعار">
                        ✕
                    </button>
                </div>
            `;

            container.appendChild(toast);

            setTimeout(() => {
                toast.classList.add('show');
            }, 100);

            setTimeout(() => {
                if (toast.parentNode) {
                    hideToast(toast.querySelector('.toast-close'));
                }
            }, adjustedDuration);

            return toast;
        }

        // دالة إخفاء الإشعار
        function hideToast(closeButton) {
            const toast = closeButton.closest('.toast');
            if (toast) {
                const isMobile = window.innerWidth <= 768;
                
                if (isMobile) {
                    toast.style.transform = 'translateY(-100px)';
                    toast.style.opacity = '0';
                } else {
                    toast.classList.remove('show');
                }
                
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, isMobile ? 300 : 400);
            }
        }

        // دوال الاختبار
        function testToast(message, type) {
            showToast(message, type);
        }

        function testLongMessage() {
            const longMessage = 'هذه رسالة طويلة جداً لاختبار كيفية التعامل مع النصوص الطويلة في الإشعارات على الهواتف المحمولة. يجب أن تظهر بشكل صحيح ومقروء حتى لو كانت طويلة.';
            showToast(longMessage, 'info', 5000);
        }

        function testMultipleToasts() {
            showToast('الإشعار الأول', 'success');
            setTimeout(() => showToast('الإشعار الثاني', 'warning'), 500);
            setTimeout(() => showToast('الإشعار الثالث', 'error'), 1000);
            setTimeout(() => showToast('الإشعار الرابع', 'info'), 1500);
        }

        function clearAllToasts() {
            const toasts = document.querySelectorAll('.toast');
            toasts.forEach(toast => {
                const closeBtn = toast.querySelector('.toast-close');
                if (closeBtn) {
                    hideToast(closeBtn);
                }
            });
        }
    </script>
</body>
</html>
