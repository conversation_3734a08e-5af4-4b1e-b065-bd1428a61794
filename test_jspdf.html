<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار jsPDF</title>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover { background: #0056b3; }
        .test-button.success { background: #28a745; }
        .test-button.danger { background: #dc3545; }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .attempt {
            margin: 10px 0;
            padding: 10px;
            border-left: 4px solid #007bff;
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📄 اختبار jsPDF المتقدم</h1>
        
        <div id="status" class="status info">
            🔄 جاري فحص jsPDF...
        </div>
        
        <div style="text-align: center; margin: 20px 0;">
            <button class="test-button" onclick="checkJsPDF()">🔍 فحص jsPDF</button>
            <button class="test-button success" onclick="loadFromCDN()">📥 تحميل من CDN</button>
            <button class="test-button success" onclick="loadFromUnpkg()">📥 تحميل من unpkg</button>
            <button class="test-button success" onclick="loadFromJSDelivr()">📥 تحميل من jsdelivr</button>
            <button class="test-button danger" onclick="location.reload()">🔄 إعادة تحميل</button>
        </div>
        
        <div id="attempts"></div>
        <div id="log" class="log"></div>
    </div>

    <script>
        let attemptCount = 0;
        const log = [];
        
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            log.push(`[${timestamp}] ${message}`);
            
            const logDiv = document.getElementById('log');
            logDiv.innerHTML = log.join('\n');
            logDiv.scrollTop = logDiv.scrollHeight;
            
            console.log(message);
        }
        
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = 'status ' + type;
        }
        
        function addAttempt(source, success, details) {
            attemptCount++;
            const attemptsDiv = document.getElementById('attempts');
            
            const attemptDiv = document.createElement('div');
            attemptDiv.className = 'attempt';
            attemptDiv.innerHTML = `
                <strong>المحاولة ${attemptCount}: ${source}</strong><br>
                النتيجة: ${success ? '✅ نجح' : '❌ فشل'}<br>
                التفاصيل: ${details}
            `;
            
            attemptsDiv.appendChild(attemptDiv);
        }
        
        function checkJsPDF() {
            addLog('🔍 بدء فحص jsPDF...');

            const checks = {
                'window.jsPDF': typeof window.jsPDF !== 'undefined',
                'global jsPDF': typeof jsPDF !== 'undefined',
                'window.jspdf': typeof window.jspdf !== 'undefined',
                'window.jspdf.jsPDF': window.jspdf && typeof window.jspdf.jsPDF !== 'undefined'
            };

            let found = false;
            Object.entries(checks).forEach(([name, result]) => {
                addLog(`  ${result ? '✅' : '❌'} ${name}: ${result}`);
                if (result) found = true;
            });

            // معلومات إضافية عن window.jspdf
            if (window.jspdf) {
                addLog(`  📋 محتويات window.jspdf: ${Object.keys(window.jspdf).join(', ')}`);
            }

            if (found) {
                updateStatus('🎉 jsPDF متوفرة!', 'success');
                testJsPDFFunction();
            } else {
                updateStatus('❌ jsPDF غير متوفرة', 'error');
                addLog('💡 جرب تحميلها من أحد المصادر أعلاه');
            }
        }
        
        function testJsPDFFunction() {
            try {
                // البحث عن jsPDF في جميع الأماكن المحتملة
                let jsPDFInstance = null;
                let source = '';

                if (typeof window.jsPDF !== 'undefined') {
                    jsPDFInstance = window.jsPDF;
                    source = 'window.jsPDF';
                } else if (typeof jsPDF !== 'undefined') {
                    jsPDFInstance = jsPDF;
                    source = 'global jsPDF';
                } else if (typeof window.jspdf !== 'undefined') {
                    jsPDFInstance = window.jspdf.jsPDF;
                    source = 'window.jspdf.jsPDF';
                } else if (window.jspdf && window.jspdf.jsPDF) {
                    jsPDFInstance = window.jspdf.jsPDF;
                    source = 'window.jspdf.jsPDF (nested)';
                }

                if (!jsPDFInstance) {
                    throw new Error('لم يتم العثور على jsPDF في أي مكان');
                }

                addLog(`✅ تم العثور على jsPDF في: ${source}`);

                const doc = new jsPDFInstance();
                doc.text('اختبار jsPDF', 20, 20);

                addLog('✅ تم إنشاء PDF تجريبي بنجاح');

                // إضافة زر التحميل
                const statusDiv = document.getElementById('status');
                statusDiv.innerHTML = `
                    🎉 jsPDF تعمل بشكل مثالي!<br>
                    <small>المصدر: ${source}</small><br>
                    <button class="test-button success" onclick="downloadPDF()">📥 تحميل PDF تجريبي</button>
                `;

            } catch (error) {
                addLog('❌ خطأ في اختبار jsPDF: ' + error.message);

                // معلومات تشخيصية إضافية
                addLog('🔍 معلومات تشخيصية:');
                addLog(`  typeof window.jsPDF: ${typeof window.jsPDF}`);
                addLog(`  typeof jsPDF: ${typeof jsPDF}`);
                addLog(`  typeof window.jspdf: ${typeof window.jspdf}`);
                if (window.jspdf) {
                    addLog(`  window.jspdf keys: ${Object.keys(window.jspdf).join(', ')}`);
                }

                updateStatus('❌ jsPDF لا تعمل بشكل صحيح', 'error');
            }
        }
        
        function loadFromCDN() {
            addLog('🔄 محاولة تحميل من CDN...');
            loadScript(
                'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js',
                'CDN (cdnjs)'
            );
        }
        
        function loadFromUnpkg() {
            addLog('🔄 محاولة تحميل من unpkg...');
            loadScript(
                'https://unpkg.com/jspdf@2.5.1/dist/jspdf.umd.min.js',
                'unpkg'
            );
        }
        
        function loadFromJSDelivr() {
            addLog('🔄 محاولة تحميل من jsdelivr...');
            loadScript(
                'https://cdn.jsdelivr.net/npm/jspdf@2.5.1/dist/jspdf.umd.min.js',
                'jsdelivr'
            );
        }
        
        function loadScript(src, sourceName) {
            const script = document.createElement('script');
            script.src = src;
            
            script.onload = function() {
                addLog(`✅ تم تحميل jsPDF من ${sourceName} بنجاح`);
                addAttempt(sourceName, true, 'تم التحميل بنجاح');
                
                setTimeout(() => {
                    checkJsPDF();
                }, 100);
            };
            
            script.onerror = function() {
                addLog(`❌ فشل تحميل jsPDF من ${sourceName}`);
                addAttempt(sourceName, false, 'فشل في التحميل');
            };
            
            document.head.appendChild(script);
        }
        
        function downloadPDF() {
            try {
                // البحث عن jsPDF في جميع الأماكن المحتملة
                let jsPDFInstance = null;
                let source = '';

                if (typeof window.jsPDF !== 'undefined') {
                    jsPDFInstance = window.jsPDF;
                    source = 'window.jsPDF';
                } else if (typeof jsPDF !== 'undefined') {
                    jsPDFInstance = jsPDF;
                    source = 'global jsPDF';
                } else if (typeof window.jspdf !== 'undefined' && window.jspdf.jsPDF) {
                    jsPDFInstance = window.jspdf.jsPDF;
                    source = 'window.jspdf.jsPDF';
                } else if (window.jspdf && window.jspdf.jsPDF) {
                    jsPDFInstance = window.jspdf.jsPDF;
                    source = 'window.jspdf.jsPDF (nested)';
                }

                if (!jsPDFInstance) {
                    throw new Error('لم يتم العثور على jsPDF');
                }

                const doc = new jsPDFInstance();

                doc.text('اختبار jsPDF', 20, 20);
                doc.text('تم إنشاء هذا الملف بنجاح', 20, 40);
                doc.text('التاريخ: ' + new Date().toLocaleDateString('ar'), 20, 60);
                doc.text('الوقت: ' + new Date().toLocaleTimeString('ar'), 20, 80);
                doc.text('المصدر: ' + source, 20, 100);

                doc.save('jspdf-test.pdf');
                addLog('✅ تم تحميل PDF بنجاح من: ' + source);

            } catch (error) {
                addLog('❌ خطأ في تحميل PDF: ' + error.message);
                alert('خطأ في تحميل PDF: ' + error.message);
            }
        }
        
        // فحص تلقائي عند تحميل الصفحة
        window.addEventListener('load', function() {
            addLog('🚀 بدء اختبار jsPDF...');
            setTimeout(checkJsPDF, 500);
        });
        
        // محاولة تحميل تلقائي إذا لم تكن متوفرة
        window.addEventListener('load', function() {
            setTimeout(() => {
                if (typeof jsPDF === 'undefined' && typeof window.jsPDF === 'undefined') {
                    addLog('🔄 jsPDF غير متوفرة، محاولة تحميل تلقائي...');
                    loadFromUnpkg();
                }
            }, 1000);
        });
    </script>
</body>
</html>
