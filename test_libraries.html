<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار المكتبات</title>
    
    <!-- تحميل المكتبات -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>

    <!-- jsPDF مع مصادر متعددة -->
    <script src="https://unpkg.com/jspdf@2.5.1/dist/jspdf.umd.min.js"
            onerror="loadJsPDFBackup()"></script>

    <script src="https://unpkg.com/html2canvas@1.4.1/dist/html2canvas.min.js"></script>

    <script>
        // تحميل jsPDF من مصدر بديل
        function loadJsPDFBackup() {
            console.log('🔄 تحميل jsPDF من مصدر بديل...');

            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js';
            script.onload = () => {
                console.log('✅ تم تحميل jsPDF من CDN');
                window.jsPDFLoaded = true;
                // إعادة فحص المكتبات
                setTimeout(checkLibraries, 500);
            };
            script.onerror = () => {
                console.error('❌ فشل تحميل jsPDF من CDN أيضاً');
                // محاولة أخيرة
                loadJsPDFFromJSDelivr();
            };
            document.head.appendChild(script);
        }

        function loadJsPDFFromJSDelivr() {
            console.log('🔄 محاولة أخيرة من jsdelivr...');

            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/jspdf@2.5.1/dist/jspdf.umd.min.js';
            script.onload = () => {
                console.log('✅ تم تحميل jsPDF من jsdelivr');
                window.jsPDFLoaded = true;
                setTimeout(checkLibraries, 500);
            };
            script.onerror = () => {
                console.error('❌ فشل تحميل jsPDF من جميع المصادر');
            };
            document.head.appendChild(script);
        }
    </script>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover { background: #0056b3; }
        .test-button.success { background: #28a745; }
        .test-button.danger { background: #dc3545; }
        
        .library-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
        }
        
        .progress {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #007bff, #0056b3);
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📚 اختبار تحميل المكتبات</h1>
        
        <div id="loadingStatus" class="status info">
            🔄 جاري فحص المكتبات...
        </div>
        
        <div class="progress">
            <div id="progressBar" class="progress-bar"></div>
        </div>
        
        <div id="libraryStatus"></div>
        
        <div style="text-align: center; margin: 20px 0;">
            <button class="test-button" onclick="checkLibraries()">🔍 فحص المكتبات</button>
            <button class="test-button success" onclick="testJQuery()">🧪 اختبار jQuery</button>
            <button class="test-button success" onclick="testJsPDF()">📄 اختبار jsPDF</button>
            <button class="test-button success" onclick="testHtml2Canvas()">🖼️ اختبار html2canvas</button>
            <button class="test-button danger" onclick="location.reload()">🔄 إعادة تحميل</button>
        </div>
        
        <div id="testResults"></div>
    </div>

    <script>
        let librariesLoaded = 0;
        const totalLibraries = 3;
        
        function updateProgress() {
            const percentage = (librariesLoaded / totalLibraries) * 100;
            document.getElementById('progressBar').style.width = percentage + '%';
        }
        
        function checkLibraries() {
            librariesLoaded = 0;
            const statusDiv = document.getElementById('libraryStatus');
            const loadingDiv = document.getElementById('loadingStatus');
            
            const libraries = {
                'jQuery': {
                    check: () => typeof window.$ !== 'undefined',
                    version: () => window.$ ? $.fn.jquery : null,
                    test: testJQuery
                },
                'jsPDF': {
                    check: () => {
                        // فحص متعدد لـ jsPDF
                        return typeof window.jsPDF !== 'undefined' ||
                               typeof jsPDF !== 'undefined' ||
                               (window.jspdf && typeof window.jspdf.jsPDF !== 'undefined') ||
                               window.jsPDFLoaded === true;
                    },
                    version: () => {
                        if (typeof window.jsPDF !== 'undefined') return 'متوفرة (window.jsPDF)';
                        if (typeof jsPDF !== 'undefined') return 'متوفرة (global jsPDF)';
                        if (window.jspdf && typeof window.jspdf.jsPDF !== 'undefined') return 'متوفرة (window.jspdf.jsPDF)';
                        if (window.jsPDFLoaded) return 'متوفرة (تم التحميل)';
                        return null;
                    },
                    test: testJsPDF
                },
                'html2canvas': {
                    check: () => typeof window.html2canvas !== 'undefined',
                    version: () => window.html2canvas ? 'متوفرة' : null,
                    test: testHtml2Canvas
                }
            };
            
            let statusHTML = '';
            let allLoaded = true;
            
            Object.entries(libraries).forEach(([name, lib]) => {
                const isLoaded = lib.check();
                const version = lib.version();
                
                if (isLoaded) {
                    librariesLoaded++;
                    statusHTML += `
                        <div class="status success">
                            ✅ ${name} - محملة بنجاح
                            ${version ? `<br><small>الإصدار: ${version}</small>` : ''}
                        </div>
                    `;
                } else {
                    allLoaded = false;
                    statusHTML += `
                        <div class="status error">
                            ❌ ${name} - غير محملة
                        </div>
                    `;
                }
            });
            
            statusDiv.innerHTML = statusHTML;
            updateProgress();
            
            if (allLoaded) {
                loadingDiv.innerHTML = '🎉 جميع المكتبات محملة بنجاح!';
                loadingDiv.className = 'status success';
            } else {
                loadingDiv.innerHTML = `⚠️ ${librariesLoaded}/${totalLibraries} مكتبات محملة`;
                loadingDiv.className = 'status warning';
            }
            
            return allLoaded;
        }
        
        function testJQuery() {
            const resultsDiv = document.getElementById('testResults');
            
            if (typeof $ === 'undefined') {
                resultsDiv.innerHTML = '<div class="status error">❌ jQuery غير محملة</div>';
                return;
            }
            
            try {
                // اختبار بسيط لـ jQuery
                const testElement = $('<div>').text('اختبار jQuery').addClass('test-element');
                $('body').append(testElement);
                
                // إزالة العنصر
                testElement.remove();
                
                resultsDiv.innerHTML = `
                    <div class="status success">
                        ✅ jQuery تعمل بشكل صحيح
                        <div class="library-info">
                            الإصدار: ${$.fn.jquery}<br>
                            الوظائف المتاحة: ${Object.keys($).length} وظيفة
                        </div>
                    </div>
                `;
            } catch (error) {
                resultsDiv.innerHTML = `<div class="status error">❌ خطأ في jQuery: ${error.message}</div>`;
            }
        }
        
        function testJsPDF() {
            const resultsDiv = document.getElementById('testResults');

            // فحص متعدد لـ jsPDF
            let jsPDFInstance = null;
            let source = '';

            if (typeof window.jsPDF !== 'undefined') {
                jsPDFInstance = window.jsPDF;
                source = 'window.jsPDF';
            } else if (typeof jsPDF !== 'undefined') {
                jsPDFInstance = jsPDF;
                source = 'global jsPDF';
            } else if (window.jspdf && typeof window.jspdf.jsPDF !== 'undefined') {
                jsPDFInstance = window.jspdf.jsPDF;
                source = 'window.jspdf.jsPDF';
            }

            if (!jsPDFInstance) {
                resultsDiv.innerHTML = `
                    <div class="status error">
                        ❌ jsPDF غير محملة
                        <div class="library-info">
                            فحص تفصيلي:<br>
                            • window.jsPDF: ${typeof window.jsPDF}<br>
                            • global jsPDF: ${typeof jsPDF}<br>
                            • window.jspdf: ${typeof window.jspdf}<br>
                            • window.jspdf.jsPDF: ${window.jspdf ? typeof window.jspdf.jsPDF : 'N/A'}<br>
                            • jsPDFLoaded: ${window.jsPDFLoaded}<br>
                            ${window.jspdf ? `• محتويات window.jspdf: ${Object.keys(window.jspdf).join(', ')}<br>` : ''}
                            <button class="test-button" onclick="loadJsPDFBackup()">🔄 إعادة المحاولة</button>
                        </div>
                    </div>
                `;
                return;
            }

            try {
                // إنشاء PDF تجريبي
                const doc = new jsPDFInstance();
                doc.text('اختبار jsPDF', 20, 20);
                doc.text('تم إنشاء هذا الملف بنجاح', 20, 40);

                resultsDiv.innerHTML = `
                    <div class="status success">
                        ✅ jsPDF تعمل بشكل صحيح
                        <div class="library-info">
                            تم إنشاء PDF تجريبي بنجاح<br>
                            المصدر: ${source}<br>
                            <button class="test-button" onclick="downloadTestPDF()">📥 تحميل PDF تجريبي</button>
                        </div>
                    </div>
                `;
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="status error">
                        ❌ خطأ في jsPDF: ${error.message}
                        <div class="library-info">
                            المصدر المحاول: ${source}<br>
                            <button class="test-button" onclick="loadJsPDFBackup()">🔄 إعادة المحاولة</button>
                        </div>
                    </div>
                `;
            }
        }
        
        function testHtml2Canvas() {
            const resultsDiv = document.getElementById('testResults');
            
            if (typeof html2canvas === 'undefined') {
                resultsDiv.innerHTML = '<div class="status error">❌ html2canvas غير محملة</div>';
                return;
            }
            
            try {
                // اختبار تحويل عنصر إلى صورة
                const testElement = document.querySelector('.container');
                
                html2canvas(testElement, { scale: 0.5 }).then(canvas => {
                    resultsDiv.innerHTML = `
                        <div class="status success">
                            ✅ html2canvas تعمل بشكل صحيح
                            <div class="library-info">
                                تم إنشاء صورة بحجم: ${canvas.width}x${canvas.height}<br>
                                <button class="test-button" onclick="downloadTestImage()">📥 تحميل صورة تجريبية</button>
                            </div>
                        </div>
                    `;
                    
                    // حفظ الصورة للتحميل
                    window.testCanvas = canvas;
                }).catch(error => {
                    resultsDiv.innerHTML = `<div class="status error">❌ خطأ في html2canvas: ${error.message}</div>`;
                });
                
            } catch (error) {
                resultsDiv.innerHTML = `<div class="status error">❌ خطأ في html2canvas: ${error.message}</div>`;
            }
        }
        
        function downloadTestPDF() {
            try {
                // استخدام jsPDF من أي مصدر متاح
                let jsPDFInstance = null;
                let source = '';

                if (typeof window.jsPDF !== 'undefined') {
                    jsPDFInstance = window.jsPDF;
                    source = 'window.jsPDF';
                } else if (typeof jsPDF !== 'undefined') {
                    jsPDFInstance = jsPDF;
                    source = 'global jsPDF';
                } else if (window.jspdf && typeof window.jspdf.jsPDF !== 'undefined') {
                    jsPDFInstance = window.jspdf.jsPDF;
                    source = 'window.jspdf.jsPDF';
                }

                if (!jsPDFInstance) {
                    alert('❌ jsPDF غير متوفرة للتحميل');
                    return;
                }

                const doc = new jsPDFInstance();
                doc.text('اختبار تحميل PDF', 20, 20);
                doc.text('تم إنشاء هذا الملف بواسطة jsPDF', 20, 40);
                doc.text('التاريخ: ' + new Date().toLocaleDateString('ar'), 20, 60);
                doc.text('الوقت: ' + new Date().toLocaleTimeString('ar'), 20, 80);
                doc.text('المصدر: ' + source, 20, 100);
                doc.save('test-pdf.pdf');

                console.log('✅ تم تحميل PDF بنجاح من: ' + source);
            } catch (error) {
                console.error('❌ خطأ في تحميل PDF:', error);
                alert('خطأ في تحميل PDF: ' + error.message);
            }
        }
        
        function downloadTestImage() {
            try {
                if (window.testCanvas) {
                    const link = document.createElement('a');
                    link.download = 'test-image.png';
                    link.href = window.testCanvas.toDataURL();
                    link.click();
                } else {
                    alert('لا توجد صورة للتحميل');
                }
            } catch (error) {
                alert('خطأ في تحميل الصورة: ' + error.message);
            }
        }
        
        // فحص تلقائي عند تحميل الصفحة
        window.addEventListener('load', function() {
            setTimeout(checkLibraries, 500);
        });
        
        // فحص دوري كل 5 ثواني
        setInterval(function() {
            const currentLoaded = librariesLoaded;
            checkLibraries();
            
            if (librariesLoaded > currentLoaded) {
                console.log('🎉 تم تحميل مكتبة جديدة!');
            }
        }, 5000);
    </script>
</body>
</html>
