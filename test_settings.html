<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الإعدادات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 3px;
            font-family: monospace;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار نظام الإعدادات</h1>
        
        <div class="test-section">
            <h3>📋 اختبار تحميل الإعدادات</h3>
            <button class="test-button" onclick="testLoadSettings()">تحميل الإعدادات</button>
            <div id="loadResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>💾 اختبار حفظ الإعدادات</h3>
            <button class="test-button" onclick="testSaveSettings()">حفظ إعدادات تجريبية</button>
            <div id="saveResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🎨 اختبار إعدادات الألوان</h3>
            <button class="test-button" onclick="testColorSettings()">تطبيق ألوان تجريبية</button>
            <div id="colorResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>⚙️ اختبار إعدادات الميزات</h3>
            <button class="test-button" onclick="testFeatureSettings()">تطبيق ميزات تجريبية</button>
            <div id="featureResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🔄 اختبار إعادة التعيين</h3>
            <button class="test-button" onclick="testResetSettings()">إعادة تعيين الإعدادات</button>
            <div id="resetResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📊 عرض الإعدادات الحالية</h3>
            <button class="test-button" onclick="showCurrentSettings()">عرض الإعدادات</button>
            <div id="currentResult" class="result"></div>
        </div>
    </div>

    <script>
        // محاكاة نظام الإعدادات
        let userSettings = {
            colors: {
                primaryColor: '#007bff',
                secondaryColor: '#6c757d',
                successColor: '#28a745',
                dangerColor: '#dc3545',
                warningColor: '#ffc107',
                infoColor: '#17a2b8'
            },
            features: {
                animationsEnabled: true,
                particlesEnabled: false,
                interactiveSoundsEnabled: false,
                reducedMotion: false,
                soundEnabled: false,
                soundVolume: 30,
                autoRefresh: true,
                refreshInterval: 30,
                itemsPerPage: 25,
                notificationsEnabled: true,
                notificationPosition: 'top-right',
                notificationDuration: 5,
                autoSave: true,
                backupEnabled: false
            }
        };

        function testLoadSettings() {
            try {
                const saved = localStorage.getItem('userSettings');
                if (saved) {
                    const parsed = JSON.parse(saved);
                    userSettings = { ...userSettings, ...parsed };
                    showResult('loadResult', '✅ تم تحميل الإعدادات بنجاح', 'success');
                } else {
                    showResult('loadResult', 'ℹ️ لا توجد إعدادات محفوظة', 'info');
                }
            } catch (error) {
                showResult('loadResult', '❌ خطأ في تحميل الإعدادات: ' + error.message, 'error');
            }
        }

        function testSaveSettings() {
            try {
                // تعديل بعض الإعدادات للاختبار
                userSettings.features.soundVolume = Math.floor(Math.random() * 100);
                userSettings.features.refreshInterval = Math.floor(Math.random() * 60) + 5;
                
                localStorage.setItem('userSettings', JSON.stringify(userSettings));
                showResult('saveResult', '✅ تم حفظ الإعدادات بنجاح', 'success');
            } catch (error) {
                showResult('saveResult', '❌ خطأ في حفظ الإعدادات: ' + error.message, 'error');
            }
        }

        function testColorSettings() {
            try {
                // تطبيق ألوان عشوائية
                const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3'];
                userSettings.colors.primaryColor = colors[Math.floor(Math.random() * colors.length)];
                
                document.documentElement.style.setProperty('--primary-color', userSettings.colors.primaryColor);
                showResult('colorResult', '🎨 تم تطبيق لون جديد: ' + userSettings.colors.primaryColor, 'success');
            } catch (error) {
                showResult('colorResult', '❌ خطأ في تطبيق الألوان: ' + error.message, 'error');
            }
        }

        function testFeatureSettings() {
            try {
                // تبديل بعض الميزات
                userSettings.features.animationsEnabled = !userSettings.features.animationsEnabled;
                userSettings.features.notificationsEnabled = !userSettings.features.notificationsEnabled;
                
                showResult('featureResult', '⚙️ تم تبديل الميزات - الحركات: ' + 
                    (userSettings.features.animationsEnabled ? 'مفعلة' : 'معطلة') + 
                    ', الإشعارات: ' + 
                    (userSettings.features.notificationsEnabled ? 'مفعلة' : 'معطلة'), 'success');
            } catch (error) {
                showResult('featureResult', '❌ خطأ في تطبيق الميزات: ' + error.message, 'error');
            }
        }

        function testResetSettings() {
            try {
                localStorage.removeItem('userSettings');
                userSettings = {
                    colors: {
                        primaryColor: '#007bff',
                        secondaryColor: '#6c757d',
                        successColor: '#28a745',
                        dangerColor: '#dc3545',
                        warningColor: '#ffc107',
                        infoColor: '#17a2b8'
                    },
                    features: {
                        animationsEnabled: true,
                        particlesEnabled: false,
                        interactiveSoundsEnabled: false,
                        reducedMotion: false,
                        soundEnabled: false,
                        soundVolume: 30,
                        autoRefresh: true,
                        refreshInterval: 30,
                        itemsPerPage: 25,
                        notificationsEnabled: true,
                        notificationPosition: 'top-right',
                        notificationDuration: 5,
                        autoSave: true,
                        backupEnabled: false
                    }
                };
                showResult('resetResult', '🔄 تم إعادة تعيين الإعدادات للقيم الافتراضية', 'success');
            } catch (error) {
                showResult('resetResult', '❌ خطأ في إعادة التعيين: ' + error.message, 'error');
            }
        }

        function showCurrentSettings() {
            try {
                const settingsText = JSON.stringify(userSettings, null, 2);
                showResult('currentResult', '📊 الإعدادات الحالية:\n' + settingsText, 'info');
            } catch (error) {
                showResult('currentResult', '❌ خطأ في عرض الإعدادات: ' + error.message, 'error');
            }
        }

        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = 'result ' + type;
        }

        // تحميل الإعدادات عند بدء الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            testLoadSettings();
        });
    </script>
</body>
</html>
